
{% extends 'main.html' %}

{% load static %}

{% block content %}



    <div class="authincation h-100">
    <div class="container h-100">
            <div class="row justify-content-center h-100 align-items-center">
                <div class="col-md-6">
    <div class="authincation-content">
        <div class="row no-gutters">
            <div class="col-xl-12">
                <div class="auth-form">
                    <div class="text-center mb-3">
                        <img src="public/images/logo-full.png" alt="">
                    </div>
                    <h4 class="text-center mb-4">Sign into your account</h4>

                    {% for message in messages %}

                    {{message}}

                    {% endfor %}
                    <form  method = "post">
                        {% csrf_token %}
                      
                        <div class="form-group">
                            <label class="mb-1"><strong>Email</strong></label>
                            <input type="email" class="form-control" name ="email">
                        </div>
                        <div class="form-group">
                            <label class="mb-1"><strong>Password</strong></label>
                            <input type="password" class="form-control" name ="password">
                        </div>
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-block">Login</button>
                        </div>
                    </form>
                    <div class="new-account mt-3">
                        <p>Dont't have an account? <a class="text-primary" href="page-login.html">Sign Up</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
            </div>
        </div>
    </div>

{% endblock %}
