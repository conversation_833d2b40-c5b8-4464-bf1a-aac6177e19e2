/**
* jQuery asGradient v0.3.3
* https://github.com/amazingSurge/jquery-asGradient
*
* Copyright (c) amazingSurge
* Released under the LGPL-3.0 license
*/
!function(t,e){if("function"==typeof define&&define.amd)define("AsGradient",["exports","jquery","jquery-asColor"],e);else if("undefined"!=typeof exports)e(exports,require("jquery"),require("jquery-asColor"));else{var n={exports:{}};e(n.exports,t.jQuery,t.AsColor),t.AsGradient=n.exports}}(this,function(t,e,n){"use strict";function r(t){return t&&t.__esModule?t:{default:t}}function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function i(){var t=window.navigator.userAgent,e="";return/MSIE/g.test(t)?e="-ms-":/Firefox/g.test(t)?e="-moz-":/(WebKit)/i.test(t)?e="-webkit-":/Opera/g.test(t)&&(e="-o-"),e}function s(t){var e={top:"bottom",right:"left",bottom:"top",left:"right","right top":"left bottom","top right":"bottom left","bottom right":"top left","right bottom":"left top","left bottom":"right top","bottom left":"top right","top left":"bottom right","left top":"right bottom"};return e.hasOwnProperty(t)?e[t]:t}function a(t){return/^(top|left|right|bottom)$/i.test(t)}Object.defineProperty(t,"__esModule",{value:!0});var u=r(e),l=r(n),p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),h={prefixes:["-webkit-","-moz-","-ms-","-o-"],forceStandard:!0,angleUseKeyword:!0,emptyString:"",degradationFormat:!1,cleanPosition:!0,color:{format:!1,hexUseName:!1,reduceAlpha:!0,shortenHex:!0,zeroAlphaAsTransparent:!1,invalidValue:{r:0,g:0,b:0,a:1}}};String.prototype.includes||(String.prototype.includes=function(t,e){return"number"!=typeof e&&(e=0),!(e+t.length>this.length)&&-1!==this.indexOf(t,e)});var c={"to top":0,"to right":90,"to bottom":180,"to left":270,"to right top":45,"to top right":45,"to bottom right":135,"to right bottom":135,"to left bottom":225,"to bottom left":225,"to top left":315,"to left top":315},v=function(t){var e={};for(var n in t)t.hasOwnProperty(n)&&(e[t[n]]=n);return e}(c),g=function(){var t=/(?:rgba|rgb|hsla|hsl)\s*\([\s\d\.,%]+\)|#[a-z0-9]{3,6}|[a-z]+/i,e=/\d{1,3}%/i,n=/(?:to ){0,1}(?:(?:top|left|right|bottom)\s*){1,2}|\d+deg/i,r=new RegExp("("+t.source+")\\s*("+e.source+"){0,1}","i"),o=new RegExp(r.source,"gi"),i=new RegExp("(?:("+n.source+")){0,1}\\s*,{0,1}\\s*(.*?)\\s*","i");return{FULL:new RegExp("^(-webkit-|-moz-|-ms-|-o-){0,1}(linear|radial|repeating-linear)-gradient\\s*\\(\\s*("+i.source+")\\s*\\)$","i"),ANGLE:n,COLOR:t,POSITION:e,STOP:r,STOPS:o,PARAMETERS:new RegExp("^"+i.source+"$","i")}}(),d={matchString:function(t){var e=this.parseString(t);return!!(e&&e.value&&e.value.stops&&e.value.stops.length>1)},parseString:function(t){t=u.default.trim(t);var e=void 0;if(null!==(e=g.FULL.exec(t))){var n=this.parseParameters(e[3]);return{prefix:void 0===e[1]?null:e[1],type:e[2],value:n}}return!1},parseParameters:function(t){var e=void 0;if(null!==(e=g.PARAMETERS.exec(t))){var n=this.parseStops(e[2]);return{angle:void 0===e[1]?0:e[1],stops:n}}return!1},parseStops:function(t){var e=this,n=void 0,r=[];return null!==(n=t.match(g.STOPS))&&(u.default.each(n,function(t,n){var o=e.parseStop(n);o&&r.push(o)}),r)},formatStops:function(t,e){for(var n=void 0,r=[],o=[],i=[],s=void 0,a=0;a<t.length;a++)s=void 0===(n=t[a]).position||null===n.position?0===a?0:a===t.length-1?1:void 0:n.position,o.push(s),i.push(n.color.toString());o=function(t){for(var e=null,n=void 0,r=0;r<t.length;r++)if(isNaN(t[r])){if(null===e){e=r;continue}}else if(e){n=(t[r]-t[e-1])/(r-e+1);for(var o=e;o<r;o++)t[o]=t[e-1]+(o-e+1)*n;e=null}return t}(o);for(var u=0;u<t.length;u++)s=e&&(0===u&&0===o[u]||u===t.length-1&&1===o[u])?"":" "+this.formatPosition(o[u]),r.push(i[u]+s);return r.join(", ")},parseStop:function(t){var e=void 0;if(null!==(e=g.STOP.exec(t))){var n=this.parsePosition(e[2]);return{color:e[1],position:n}}return!1},parsePosition:function(t){return"string"==typeof t&&"%"===t.substr(-1)&&(t=parseFloat(t.slice(0,-1)/100)),void 0!==t&&null!==t?parseFloat(t,10):null},formatPosition:function(t){return parseInt(100*t,10)+"%"},parseAngle:function(t,e){if("string"==typeof t&&t.includes("deg")&&(t=t.replace("deg","")),isNaN(t)||e&&(t=this.fixOldAngle(t)),"string"==typeof t){var n=t.split(" "),r=[];for(var o in n)a(n[o])&&r.push(n[o].toLowerCase());var i=r.join(" ");t.includes("to ")||(i=s(i)),i="to "+i,c.hasOwnProperty(i)&&(t=c[i])}var u=parseFloat(t,10);return u>360?u%=360:u<0&&0!==(u%=-360)&&(u+=360),u},fixOldAngle:function(t){return t=parseFloat(t),t=Math.abs(450-t)%360,t=parseFloat(t.toFixed(3))},formatAngle:function(t,e,n){return t=parseInt(t,10),n&&v.hasOwnProperty(t)?(t=v[t],e&&(t=s(t.substr(3)))):(e&&(t=this.fixOldAngle(t)),t+="deg"),t}},y=function(){function t(e,n,r){o(this,t),this.color=(0,l.default)(e,r.options.color),this.position=d.parsePosition(n),this.id=++r._stopIdCount,this.gradient=r}return f(t,[{key:"setPosition",value:function(t){var e=d.parsePosition(t);this.position!==e&&(this.position=e,this.gradient.reorder())}},{key:"setColor",value:function(t){this.color.fromString(t)}},{key:"remove",value:function(){this.gradient.removeById(this.id)}}]),t}(),m={LINEAR:{parse:function(t){return{r:"%"===t[1].substr(-1)?parseInt(2.55*t[1].slice(0,-1),10):parseInt(t[1],10),g:"%"===t[2].substr(-1)?parseInt(2.55*t[2].slice(0,-1),10):parseInt(t[2],10),b:"%"===t[3].substr(-1)?parseInt(2.55*t[3].slice(0,-1),10):parseInt(t[3],10),a:1}},to:function(t,e,n){if(0===t.stops.length)return e.options.emptyString;if(1===t.stops.length)return t.stops[0].color.to(e.options.degradationFormat);var r=e.options.forceStandard,o=e._prefix;o||(r=!0),n&&-1!==u.default.inArray(n,e.options.prefixes)&&(r=!1,o=n);var i="linear-gradient("+d.formatAngle(t.angle,!r,e.options.angleUseKeyword)+", "+d.formatStops(t.stops,e.options.cleanPosition)+")";return r?i:o+i}}},b=function(){function t(e,n){o(this,t),"object"===(void 0===e?"undefined":p(e))&&void 0===n&&(n=e,e=void 0),this.value={angle:0,stops:[]},this.options=u.default.extend(!0,{},h,n),this._type="LINEAR",this._prefix=null,this.length=this.value.stops.length,this.current=0,this._stopIdCount=0,this.init(e)}return f(t,[{key:"init",value:function(t){t&&this.fromString(t)}},{key:"val",value:function(t){return void 0===t?this.toString():(this.fromString(t),this)}},{key:"angle",value:function(t){return void 0===t?this.value.angle:(this.value.angle=d.parseAngle(t),this)}},{key:"append",value:function(t,e){return this.insert(t,e,this.length)}},{key:"reorder",value:function(){this.length<2||(this.value.stops=this.value.stops.sort(function(t,e){return t.position-e.position}))}},{key:"insert",value:function(t,e,n){void 0===n&&(n=this.current);var r=new y(t,e,this);return this.value.stops.splice(n,0,r),this.length=this.length+1,this.current=n,r}},{key:"getById",value:function(t){if(this.length>0)for(var e in this.value.stops)if(t===this.value.stops[e].id)return this.value.stops[e];return!1}},{key:"removeById",value:function(t){var e=this.getIndexById(t);e&&this.remove(e)}},{key:"getIndexById",value:function(t){var e=0;for(var n in this.value.stops){if(t===this.value.stops[n].id)return e;e++}return!1}},{key:"getCurrent",value:function(){return this.value.stops[this.current]}},{key:"setCurrentById",value:function(t){var e=0;for(var n in this.value.stops)this.value.stops[n].id!==t?e++:this.current=e}},{key:"get",value:function(t){return void 0===t&&(t=this.current),t>=0&&t<this.length&&(this.current=t,this.value.stops[t])}},{key:"remove",value:function(t){void 0===t&&(t=this.current),t>=0&&t<this.length&&(this.value.stops.splice(t,1),this.length=this.length-1,this.current=t-1)}},{key:"empty",value:function(){this.value.stops=[],this.length=0,this.current=0}},{key:"reset",value:function(){this.value._angle=0,this.empty(),this._prefix=null,this._type="LINEAR"}},{key:"type",value:function(t){return"string"==typeof t&&(t=t.toUpperCase())&&void 0!==m[t]?(this._type=t,this):this._type}},{key:"fromString",value:function(t){var e=this;this.reset();var n=d.parseString(t);n&&(this._prefix=n.prefix,this.type(n.type),n.value&&(this.value.angle=d.parseAngle(n.value.angle,null!==this._prefix),u.default.each(n.value.stops,function(t,n){e.append(n.color,n.position)})))}},{key:"toString",value:function(t){return!0===t&&(t=i()),m[this.type()].to(this.value,this,t)}},{key:"matchString",value:function(t){return d.matchString(t)}},{key:"toStringWithAngle",value:function(t,e){var n=u.default.extend(!0,{},this.value);return n.angle=d.parseAngle(t),!0===e&&(e=i()),m[this.type()].to(n,this,e)}},{key:"getPrefixedStrings",value:function(){var t=[];for(var e in this.options.prefixes)Object.hasOwnProperty.call(this.options.prefixes,e)&&t.push(this.toString(this.options.prefixes[e]));return t}}],[{key:"setDefaults",value:function(t){u.default.extend(!0,h,u.default.isPlainObject(t)&&t)}}]),t}(),S={version:"0.3.3"},x=u.default.asGradient,k=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return new(Function.prototype.bind.apply(b,[null].concat(e)))};u.default.asGradient=k,u.default.asGradient.Constructor=b,u.default.extend(u.default.asGradient,{setDefaults:b.setDefaults,noConflict:function(){return u.default.asGradient=x,k}},d,S);var A=u.default.asGradient;t.default=A});
//# sourceMappingURL=jquery-asGradient.min.js.map
