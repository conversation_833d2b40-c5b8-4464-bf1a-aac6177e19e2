# Generated by Django 4.2.7 on 2025-06-19 16:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('grading', '0008_grade_status'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customuser',
            name='position',
            field=models.CharField(choices=[('student', 'Student'), ('lecturer', 'Lecturer'), ('Hod', 'Head of Department'), ('admin', 'Lecturer')], default='student', help_text="User's position in the institution", max_length=20),
        ),
        migrations.CreateModel(
            name='HodProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=255, null=True)),
                ('phone', models.Char<PERSON>ield(max_length=255, null=True)),
                ('bio', models.TextField(null=True)),
                ('image', models.ImageField(null=True, upload_to='profile-images')),
                ('gender', models.CharField(blank=True, max_length=10, null=True)),
                ('department', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='grading.department')),
                ('user', models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
