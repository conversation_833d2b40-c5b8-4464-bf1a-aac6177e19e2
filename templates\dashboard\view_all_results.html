{% extends "dashboard/base.html" %}
{% load static %}

{% block title %}All Results - {{ department.name }}{% endblock %}

{% block content %}

<div class="content-body">
    <div class="container-fluid">

        <div class="form-head d-flex flex-wrap mb-sm-4 mb-3 align-items-center">
            <div class="me-auto d-lg-block mb-3">
                <h2 class="text-black mb-0 font-w700">All Student Results - {{ department.name }}</h2>
                <p class="text-muted mb-0">
                    <i class="fas fa-table me-2"></i>
                    View and manage all student results by level and semester
                </p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header pb-0 border-0">
                        <h4 class="card-title mb-0">Results Overview</h4>
                    </div>
                    <br>

                    <!-- Filters -->
                    <div class="px-3 pb-3">
                        <form method="GET" class="bg-light rounded p-3 mb-3">
                            <div class="row g-3 align-items-center">
                                <div class="col-md-2">
                                    <h6 class="mb-0 text-muted">
                                        <i class="fas fa-filter me-2"></i>Select Filters:
                                    </h6>
                                </div>

                                <!-- Level Filter -->
                                <div class="col-md-3">
                                    <label class="form-label mb-1 text-muted small">
                                        <i class="fas fa-layer-group me-1"></i>Level <span class="text-danger">*</span>
                                    </label>
                                    <select name="level" class="form-select form-select-sm" required>
                                        <option value="">Select Level...</option>
                                        {% for level in levels %}
                                        <option value="{{ level.id }}" {% if level.id|stringformat:"s" == level_id %}selected{% endif %}>
                                            {{ level.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <!-- Semester Filter -->
                                <div class="col-md-3">
                                    <label class="form-label mb-1 text-muted small">
                                        <i class="fas fa-calendar me-1"></i>Semester <span class="text-danger">*</span>
                                    </label>
                                    <select name="semester" class="form-select form-select-sm" required>
                                        <option value="">Select Semester...</option>
                                        {% for semester in semesters %}
                                        <option value="{{ semester.id }}" {% if semester.id|stringformat:"s" == semester_id %}selected{% endif %}>
                                            {{ semester.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <!-- Search Button -->
                                <div class="col-md-4" style="margin-top: 1em;">
                                    <button type="submit" class="btn btn-primary btn-sm me-2">
                                        <i class="fas fa-table me-1"></i>View Results
                                    </button>
                                    <a href="{% url 'view_all_results' %}" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-times me-1"></i>Clear
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Results Display -->
                    {% if show_results %}
                        {% if student_results %}
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">
                                    <i class="fas fa-graduation-cap text-primary me-2"></i>
                                    Results 
                                    {% if selected_level and selected_semester %}
                                        for {{ selected_level.name }} - {{ selected_semester.name }}
                                    {% elif selected_level %}
                                        for {{ selected_level.name }}
                                    {% elif selected_semester %}
                                        for {{ selected_semester.name }}
                                    {% endif %}
                                </h5>

                                           <a href="#chartsSection" class="btn btn-outline-success btn-sm ms-3" title="See Charts">
                                    <i class="fas fa-arrow-down"></i> See Performance Charts
                                </a>
                                <span class="badge bg-primary fs-6">{{ results_count }} student{{ results_count|pluralize }}</span>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>#</th>
                                            <th>Matric Number</th>
                                            <th>Student Name</th>
                                            <th>Level</th>
                                            <th>Semester</th>
                                            <th>Course Details</th>
                                            <th>TCU</th>
                                            <th>TGP</th>
                                            <th>GPA</th>
                                            <th>Remark</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for key, data in student_results.items %}
                                            {% with result=data.result %}
                                            <tr>
                                                <td class="fw-bold text-primary">{{ forloop.counter }}</td>
                                                <td>
                                                    <div class="fw-bold text-dark">{{ result.student.matric_number }}</div>
                                                </td>
                                                <td>
                                                    <div class="fw-semibold text-dark">{{ result.student.full_name }}</div>
                                                </td>
                                                <td>
                                                    <div class="fw-semibold text-dark">{{ result.level.name }}</div>
                                                </td>
                                                <td>
                                                    <div class="fw-semibold text-dark">{{ result.semester.name }}</div>
                                                </td>
                                                <td>
                                                    <div class="course-details" style="max-width: 350px;">
                                                        <div class="table-responsive">
                                                            <table class="table table-sm table-borderless mb-0">
                                                                <thead>
                                                                    <tr class="text-muted" style="font-size: 0.75em;">
                                                                        <th>Code</th>
                                                                        <th>Score</th>
                                                                        <th>Grade</th>
                                                                        <th>Unit</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    {% for grade in data.grades %}
                                                                    <tr style="font-size: 0.85em;">
                                                                        <td class="fw-bold text-dark">{{ grade.course.code }}</td>
                                                                        <td class="text-center">{{ grade.score }}%</td>
                                                                        <td class="text-center fw-bold">{{ grade.grade }}</td>
                                                                        <td class="text-center">{{ grade.course.credit_unit }}</td>
                                                                    </tr>
                                                                    {% empty %}
                                                                    <tr>
                                                                        <td colspan="4" class="text-muted text-center">
                                                                            <small>No courses found</small>
                                                                        </td>
                                                                    </tr>
                                                                    {% endfor %}
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                        <div class="text-center mt-1 pt-1 border-top">
                                                            <small class="text-muted">
                                                                {{ data.total_courses }} course{{ data.total_courses|pluralize }}
                                                            </small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="text-center">
                                                    <div class="fw-bold">{{ result.tcu|default:0 }}</div>
                                                </td>
                                                <td class="text-center">
                                                    <div class="fw-bold">{{ result.tgp|default:0|floatformat:2 }}</div>
                                                </td>
                                                <td class="text-center">
                                                    <div class="fw-bold {% if result.gpa >= 3.5 %}text-success{% elif result.gpa >= 2.5 %}text-warning{% else %}text-danger{% endif %}">
                                                        {{ result.gpa|default:0|floatformat:2 }}
                                                    </div>
                                                </td>
                                                <td class="text-center">
                                                    {% if result.remark == 'passed' %}
                                                        <span class="text-success fw-bold">PASSED</span>
                                                    {% else %}
                                                        <span class="text-danger fw-bold">FAILED</span>
                                                    {% endif %}
                                                </td>
                                                <td class="text-center">
                                                    {% if result.status == 'published' %}
                                                        <span class="text-success fw-bold">Published</span>
                                                    {% else %}
                                                        <span class="text-muted">Draft</span>
                                                    {% endif %}
                                                </td>

                                            </tr>
                                            {% endwith %}
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            <!-- Summary Statistics -->
                            <div class="row mt-4" id="chartsSection">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h4 class="mb-1">{{ results_count }}</h4>
                                            <p class="mb-0">Total Students</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h4 class="mb-1">{{ passed_count }}</h4>
                                            <p class="mb-0">Passed</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-danger text-white">
                                        <div class="card-body text-center">
                                            <h4 class="mb-1">{{ failed_count }}</h4>
                                            <p class="mb-0">Failed</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h4 class="mb-1">{{ published_count }}</h4>
                                            <p class="mb-0">Published</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Charts Row --> 
                            <div class="row px-3 pb-3" >
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100">
                                        <div class="card-header bg-primary text-white">
                                            <i class="fas fa-chart-bar me-2"></i>Student GPA Bar Chart
                                        </div>
                                        <div class="card-body">
                                            <canvas id="gpaBarChart" height="250"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100">
                                        <div class="card-header bg-info text-white">
                                            <i class="fas fa-chart-pie me-2"></i>GPA Distribution Pie Chart
                                        </div>
                                        <div class="card-body">
                                            <canvas id="gpaPieChart" height="120"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 text-end mt-2">
                                    <a href="#" onclick="window.scrollTo({top: 0, behavior: 'smooth'}); return false;" class="btn btn-outline-secondary btn-sm" title="Back to Top">
                                        <i class="fas fa-arrow-up"></i> Back to Top
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <!-- No Results for Selected Filters -->
                        <div class="card-body text-center py-5">
                            <div class="mb-3">
                                <i class="fas fa-search fa-4x text-muted"></i>
                            </div>
                            <h5 class="text-muted">No Results Found</h5>
                            <p class="text-muted">
                                No results found for <strong>{{ selected_level.name }}</strong> - <strong>{{ selected_semester.name }}</strong>.
                                <br>Results are automatically generated when all grades for students in this level and semester are approved.
                            </p>
                            <a href="{% url 'submitted_grades' %}" class="btn btn-outline-primary me-2">
                                <i class="fas fa-check me-2"></i>
                                Approve Grades
                            </a>
                         
                        </div>
                        {% endif %}
                    {% else %}
                        <!-- Default State - No Filters Selected -->
                        <div class="card-body text-center py-5">
                            <div class="mb-4">
                                <i class="fas fa-filter fa-4x text-muted"></i>
                            </div>
                            <h4 class="text-muted mb-3">Select Level and Semester</h4>
                            <p class="text-muted mb-4">
                                Please select both <strong>Level</strong> and <strong>Semester</strong> from the filters above to view student results.
                                <br>Results will be displayed in a comprehensive table format.
                            </p>
                            {% comment %} <div class="alert alert-info border-0 d-inline-block">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Note:</strong> Results are automatically generated when grades are approved for students.
                            </div> {% endcomment %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    {% if show_results and student_results %}
    // Prepare data for charts
    const labels = [];
    const gpas = [];
    {% for key, data in student_results.items %}
        labels.push("{{ data.result.student.matric_number|escapejs }}");
        gpas.push({{ data.result.gpa|default:0|floatformat:2 }});
    {% endfor %}
    // Destroy previous charts if they exist
    if (window.gpaBarChartInstance) window.gpaBarChartInstance.destroy();
    if (window.gpaPieChartInstance) window.gpaPieChartInstance.destroy();
    // Bar Chart
    const barCtx = document.getElementById('gpaBarChart').getContext('2d');
    window.gpaBarChartInstance = new Chart(barCtx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'GPA',
                data: gpas,
                backgroundColor: 'rgba(54, 162, 235, 0.7)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: { display: false },
                title: { display: true, text: 'GPA by Matric Number' }
            },
            scales: {
                y: { beginAtZero: true, max: 4 }
            }
        }
    });
    // Pie Chart
    const pieCtx = document.getElementById('gpaPieChart').getContext('2d');
    window.gpaPieChartInstance = new Chart(pieCtx, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                label: 'GPA',
                data: gpas,
                backgroundColor: labels.map((_, i) => `hsl(${i * 360 / labels.length}, 70%, 60%)`),
                borderColor: '#fff',
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: { position: 'bottom' },
                title: { display: true, text: 'GPA Distribution by Matric Number' }
            }
        }
    });
    {% endif %}
});
</script>

<style>
.course-details {
    max-width: 400px;
}

.course-item {
    transition: all 0.2s ease;
}

.course-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table td {
    vertical-align: top;
}

.course-details .badge {
    font-size: 0.75em;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .course-details {
        max-width: 250px;
    }

    .course-item .row {
        font-size: 0.85em;
    }
}

/* Print styles */
@media print {
    .course-item {
        break-inside: avoid;
        margin-bottom: 5px !important;
        padding: 5px !important;
    }

    .badge {
        border: 1px solid #000 !important;
        color: #000 !important;
        background: transparent !important;
    }
}
</style>

<!-- Add anchor at the top of results for smooth scroll -->
<a id="topOfResults"></a>

{% endblock %}
