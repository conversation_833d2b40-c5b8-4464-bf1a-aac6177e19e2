# Generated by Django 4.2.7 on 2025-07-03 11:47

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('grading', '0016_alter_result_hod_status_alter_result_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='Cummulative_Result',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('gpa', models.FloatField(null=True)),
                ('tcu', models.IntegerField(null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('approved', 'Approved')], default='draft', max_length=20, null=True)),
                ('hod_status', models.CharField(choices=[('draft', 'Draft'), ('publish', 'Publish')], default='draft', max_length=20, null=True)),
                ('remark', models.CharField(choices=[('co', 'co'), ('withdrawn', 'withdrawn'), ('passed', 'passed'), ('passed', 'Passed'), ('failed', 'Failed')], max_length=20, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='grading.department')),
                ('level', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='grading.level')),
                ('result', models.ManyToManyField(blank=True, to='grading.result')),
                ('semester', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='grading.semester')),
                ('student', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='grading.studentprofile')),
            ],
        ),
    ]
