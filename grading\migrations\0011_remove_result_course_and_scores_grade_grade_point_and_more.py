# Generated by Django 4.2.7 on 2025-06-25 13:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('grading', '0010_alter_customuser_position'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='result',
            name='course_and_scores',
        ),
        migrations.AddField(
            model_name='grade',
            name='grade_point',
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name='result',
            name='grade_score',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='grading.grade'),
        ),
        migrations.AddField(
            model_name='result',
            name='remark',
            field=models.CharField(choices=[('co', 'co'), ('withdrawn', 'withdrawn'), ('passed', 'passed'), ('passed', 'Passed'), ('failed', 'Failed')], max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='result',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('published', 'Published')], default='draft', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='result',
            name='tcu',
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name='result',
            name='tgp',
            field=models.FloatField(null=True),
        ),
    ]
