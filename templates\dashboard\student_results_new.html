{% extends "dashboard/base.html" %}
{% load static %}

{% block title %}Student Results - {{ department.name }}{% endblock %}

{% block content %}

<div class="content-body">
    <div class="container-fluid">

        <div class="form-head d-flex flex-wrap mb-sm-4 mb-3 align-items-center">
            <div class="me-auto d-lg-block mb-3">
                <h2 class="text-black mb-0 font-w700">View {{ department.name }} Student Results</h2>
                <p class="text-muted mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Results are generated automatically when grades are approved
                </p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header pb-0 border-0">
                        <h4 class="card-title mb-0">Student Results</h4>
                    </div>
                    <br>

                    <!-- Filters -->
                    <div class="px-3 pb-3">
                        <div class="bg-light rounded p-3 mb-3">
                            <div class="row g-3 align-items-center">
                                <div class="col-md-2">
                                    <h6 class="mb-0 text-muted">
                                        <i class="fas fa-filter me-2"></i>Filter Results:
                                    </h6>
                                </div>

                                <!-- Level Filter -->
                                <div class="col-md-2">
                                    <label class="form-label mb-1 text-muted small">
                                        <i class="fas fa-layer-group me-1"></i>Level
                                    </label>
                                    <select id="levelFilter" class="form-select form-select-sm">
                                        <option value="">All Levels</option>
                                        {% for level in levels %}
                                        <option value="{{ level.id }}">{{ level.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <!-- Semester Filter -->
                                <div class="col-md-2">
                                    <label class="form-label mb-1 text-muted small">
                                        <i class="fas fa-calendar me-1"></i>Semester
                                    </label>
                                    <select id="semesterFilter" class="form-select form-select-sm">
                                        <option value="">All Semesters</option>
                                        {% for semester in semesters %}
                                        <option value="{{ semester.id }}">{{ semester.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <!-- Status Filter -->
                                <div class="col-md-3">
                                    <label class="form-label mb-1 text-muted small">
                                        <i class="fas fa-flag me-1"></i>Status
                                    </label>
                                    <select id="statusFilter" class="form-select form-select-sm">
                                        <option value="">All Status</option>
                                        <option value="draft">Draft</option>
                                        <option value="published">Published</option>
                                    </select>
                                </div>

                                <!-- Search Button -->
                                <div class="col-md-3" style="margin-top: 1em;">
                                    <button type="button" class="btn btn-primary btn-sm me-2" onclick="loadResults()">
                                        <i class="fas fa-search me-1"></i>Search
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm me-2" onclick="clearFilters()" title="Clear all filters">
                                        <i class="fas fa-times me-1"></i>Clear
                                    </button>
                                    <form method="post" action="{% url 'test_result_generation' %}" class="d-inline">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-outline-info btn-sm" title="Test result generation">
                                            <i class="fas fa-cog me-1"></i>Test
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Default State -->
                    <div class="card-body" id="defaultState">
                        <div class="text-center py-5">
                            <div class="mb-3">
                                <i class="fas fa-graduation-cap fa-4x text-muted"></i>
                            </div>
                            <h5 class="text-muted">Student Results Dashboard</h5>
                            <p class="text-muted">Use the filters above to search for student results. Results are automatically generated when grades are approved.</p>
                        </div>
                    </div>

                    <!-- Loading State -->
                    <div class="card-body text-center py-5" id="loadingState" style="display: none;">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <h5 class="text-muted">Loading Results...</h5>
                        <p class="text-muted">Please wait while we fetch the data</p>
                    </div>

                    <!-- Empty State -->
                    <div class="card-body text-center py-5" id="emptyState" style="display: none;">
                        <div class="mb-3">
                            <i class="fas fa-search fa-4x text-muted"></i>
                        </div>
                        <h5 class="text-muted">No Results Found</h5>
                        <p class="text-muted">No results match your current filter criteria. Try adjusting your filters or ensure grades have been approved.</p>
                    </div>

                    <!-- Results Table -->
                    <div class="card-body" id="resultsContainer" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">
                                <i class="fas fa-table text-primary me-2"></i>
                                Results Overview
                            </h5>
                            <span class="badge bg-primary" id="resultsCount">0 results</span>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>#</th>
                                        <th>Matric Number</th>
                                        <th>Student Name</th>
                                        <th>Level</th>
                                        <th>Semester</th>
                                        <th>Courses</th>
                                        <th>TCU</th>
                                        <th>TGP</th>
                                        <th>GPA</th>
                                        <th>Status</th>
                                        <th>Remark</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="resultsTableBody">
                                    <!-- Results will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show default state initially
    showDefaultState();
});

function showDefaultState() {
    document.getElementById('defaultState').style.display = 'block';
    document.getElementById('loadingState').style.display = 'none';
    document.getElementById('emptyState').style.display = 'none';
    document.getElementById('resultsContainer').style.display = 'none';
}

function loadResults() {
    const level = document.getElementById('levelFilter').value;
    const semester = document.getElementById('semesterFilter').value;
    const status = document.getElementById('statusFilter').value;
    
    // Show loading state
    document.getElementById('defaultState').style.display = 'none';
    document.getElementById('loadingState').style.display = 'block';
    document.getElementById('emptyState').style.display = 'none';
    document.getElementById('resultsContainer').style.display = 'none';
    
    // Build query parameters
    const params = new URLSearchParams();
    if (level) params.append('level', level);
    if (semester) params.append('semester', semester);
    if (status) params.append('status', status);
    
    fetch(`/department/fetch-student-results/?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            displayResults(data.results);
        })
        .catch(error => {
            console.error('Error loading results:', error);
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('emptyState').style.display = 'block';
            showToast('Error loading results. Please try again.', 'error');
        });
}

function clearFilters() {
    document.getElementById('levelFilter').value = '';
    document.getElementById('semesterFilter').value = '';
    document.getElementById('statusFilter').value = '';
    showDefaultState();
}

function displayResults(results) {
    const loadingState = document.getElementById('loadingState');
    const emptyState = document.getElementById('emptyState');
    const resultsContainer = document.getElementById('resultsContainer');
    const resultsCount = document.getElementById('resultsCount');
    
    // Hide loading state
    loadingState.style.display = 'none';
    
    if (results.length === 0) {
        emptyState.style.display = 'block';
        resultsContainer.style.display = 'none';
        resultsCount.textContent = '0 results';
        return;
    }
    
    // Show results container and update count
    emptyState.style.display = 'none';
    resultsContainer.style.display = 'block';
    resultsCount.textContent = `${results.length} result${results.length !== 1 ? 's' : ''}`;
    
    // Populate table
    populateResultsTable(results);
}

function populateResultsTable(results) {
    const tbody = document.getElementById('resultsTableBody');
    tbody.innerHTML = '';
    
    results.forEach((result, index) => {
        const row = document.createElement('tr');
        
        row.innerHTML = `
            <td class="fw-bold text-primary">${index + 1}</td>
            <td>
                <div class="fw-bold text-dark">${result.matric_number}</div>
                <small class="text-muted">ID: ${result.id}</small>
            </td>
            <td>
                <div class="fw-semibold text-dark">${result.student_name}</div>
                <small class="text-muted">Student</small>
            </td>
            <td>
                <span class="badge bg-info text-white">
                    <i class="fas fa-layer-group me-1"></i>
                    ${result.level}
                </span>
            </td>
            <td>
                <span class="badge bg-secondary text-white">
                    <i class="fas fa-calendar me-1"></i>
                    ${result.semester}
                </span>
            </td>
            <td class="text-center">
                <span class="badge bg-primary text-white">
                    <i class="fas fa-book me-1"></i>
                    ${result.course_count || 0}
                </span>
            </td>
            <td class="text-center">
                <span class="fw-bold fs-5">${result.total_credit_units}</span>
                <div><small class="text-muted">Units</small></div>
            </td>
            <td class="text-center">
                <span class="fw-bold text-primary fs-5">${result.total_grade_points.toFixed(2)}</span>
                <div><small class="text-muted">Points</small></div>
            </td>
            <td class="text-center">
                <span class="fw-bold ${getGPAColor(result.gpa)} fs-4">${result.gpa.toFixed(2)}</span>
                <div><small class="text-muted">GPA</small></div>
            </td>
            <td class="text-center">${getStatusBadge(result.status)}</td>
            <td class="text-center">${getRemarkBadge(result.remark)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <a href="/department/view-result/${result.id}/" class="btn btn-primary btn-sm" title="View Result">
                        <i class="fas fa-eye me-1"></i>
                        <span class="d-none d-lg-inline">View</span>
                    </a>
                    <form method="post" action="/department/publish-result/${result.id}/" class="d-inline">
                        <input type="hidden" name="csrfmiddlewaretoken" value="${document.querySelector('[name=csrfmiddlewaretoken]').value}">
                        <button type="submit" class="btn btn-${result.status === 'published' ? 'warning' : 'success'} btn-sm" 
                                title="${result.status === 'published' ? 'Unpublish' : 'Publish'} Result"
                                onclick="return confirm('${result.status === 'published' ? 'Unpublish' : 'Publish'} this result?')">
                            <i class="fas fa-${result.status === 'published' ? 'eye-slash' : 'check'} me-1"></i>
                            <span class="d-none d-lg-inline">${result.status === 'published' ? 'Unpublish' : 'Publish'}</span>
                        </button>
                    </form>
                </div>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// Helper functions
function getStatusBadge(status) {
    const badges = {
        'published': '<span class="badge bg-success fs-6"><i class="fas fa-check-circle me-1"></i>Published</span>',
        'draft': '<span class="badge bg-warning text-dark fs-6"><i class="fas fa-edit me-1"></i>Draft</span>',
    };
    return badges[status] || '<span class="badge bg-secondary fs-6">Unknown</span>';
}

function getRemarkBadge(remark) {
    return remark === 'passed' 
        ? '<span class="badge bg-success fs-6"><i class="fas fa-trophy me-1"></i>Passed</span>'
        : '<span class="badge bg-danger fs-6"><i class="fas fa-times-circle me-1"></i>Failed</span>';
}

function getGPAColor(gpa) {
    if (gpa >= 3.5) return 'text-success';
    if (gpa >= 2.5) return 'text-warning';
    return 'text-danger';
}

// Toast notification function
function showToast(message, type = 'info') {
    // Simple alert for now - can be enhanced with proper toast library
    if (type === 'error') {
        alert('Error: ' + message);
    } else {
        alert(message);
    }
}
</script>

{% endblock %}
