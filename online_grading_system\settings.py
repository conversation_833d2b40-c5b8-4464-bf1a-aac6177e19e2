"""
Django settings for online_grading_system project.

Generated by 'django-admin startproject' using Django 4.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

from pathlib import Path
import os
from django.templatetags.static import static
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _
# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-gvz8(+6)fp^3i4u5$+%1r!gw*x_)0!gag=(u4rzkowi0@217ff'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']


# Application definition

INSTALLED_APPS = [
    # Admin UI Package
    "unfold", 
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    "django.contrib.humanize", 
    'grading',

]





UNFOLD = {
    # Site branding
    "SITE_TITLE": "PTI Grading Admin",
    "SITE_HEADER": "Manage PTI Grading Admin",
    "SITE_SUBTITLE": "PTI Admin Dashboard",
    "INDEX_TITLE": "PTI Admin Dashboard",
    "SITE_URL": "/",
    "SITE_ICON": lambda request: static("img/pti.png"),
    "SITE_LOGO": lambda request: static("img/pti.png"),
    "SITE_SYMBOL": "speed",
    "environment": "production",  # Set to 'production' in production environment
    
    # Features
    "SHOW_HISTORY": True,
    
    # Login configuration 
    "LOGIN": {
        "redirect_after": lambda request: reverse_lazy("admin:index"),
        "centered": True,
    },
    
    # Assets
    "STYLES": [
        lambda request: static("css/styles.css"),
    ],
    # "SCRIPTS": [lambda request: static("js/script.js")],
    
    # Theme colors
    "COLORS": {
        "primary": {
            "50": "239 246 255",
            "100": "219 234 254", 
            "200": "191 219 254",
            "300": "147 197 253",
            "400": "96 165 250",
            "500": "0 84 203",    # Main brand color
            "600": "0 74 179",
            "700": "0 64 155",
            "800": "0 54 131",
            "900": "0 44 107",
            "950": "0 34 83",
        }
    },
    
    # Sidebar configuration
    "SIDEBAR": {
        "show_search": True,
    }
}

















MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'online_grading_system.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'online_grading_system.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = 'static/'

AUTH_USER_MODEL = 'grading.CustomUser'


STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

STATIC_ROOT = os.path.join(BASE_DIR / 'staticfiles')

MEDIA_ROOT = os.path.join(BASE_DIR / 'media')
MEDIA_URL = '/media/'


# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'





# Email settings for password reset functionality
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = "zoeyyklwchzxdgbj"
DEFAULT_FROM_EMAIL = EMAIL_HOST_USER