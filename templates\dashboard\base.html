<!DOCTYPE html>
<html lang="en">


{% load static %}

<head>	
	<!-- Title -->
	<title>Dashboard</title>

	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="author" content="DexignZone">
	<meta name="robots" content="">
	<meta name="csrf-token" content="gYh8LaNWSuELIHdqWVi45KXIX4a0vgle5gVrcbA2">
	<meta name="keywords" content="bootstrap, courses, education admin template, educational, instructors, learning, learning admin, learning admin theme, learning application, lessons, lms admin template, lms rails, quizzes ui, school admin">
	<meta name="description" content="Some description for the page"/>
	<meta property="og:title" content="Owlio - School Admission Admin Dashboard">
	<meta property="og:description" content="Owlio Laravel | Dashboard" />
	<meta property="og:image" content="../../social-image.png">
	<meta name="format-detection" content="telephone=no">

	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">


	<!-- Mobile Specific -->
	<meta name="viewport" content="width=device-width, initial-scale=1">

	<!-- Favicons Icon -->
	<link rel="icon" type="image/png" sizes="16x16" href="{% static 'public/images/favicon.png' %}">
    <link href="{% static 'public/vendor/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css' %}" rel="stylesheet" type="text/css"/>

    <link href="{% static 'public/vendor/bootstrap-select/dist/css/bootstrap-select.min.css' %}" rel="stylesheet" type="text/css"/>
    <link href="{% static 'public/css/style.css' %}" rel="stylesheet" type="text/css"/>

<!-- TOASTR CSS (CDN) -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet" />

<!-- JQUERY + TOASTR JS (CDN) -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js" defer></script>
<!-- Font Awesome CDN -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

<!-- Custom PTI Logo Styling - Universal for Dashboard and Admin -->
<style>
    /* ========================================
       PTI LOGO STYLING - UNIVERSAL COVERAGE
       ======================================== */

    /* Base Logo Styling */
    .logo-abbr, .pti-logo-abbr,
    .brand-logo img, .admin-logo img,
    .unfold-logo img, [class*="logo"] img {
        max-width: 60px !important;
        min-width: 60px !important;
        height: auto !important;
        object-fit: contain;
        transition: transform 0.3s ease, filter 0.3s ease;
        cursor: pointer;

    }

    .brand-title, .pti-logo-title {
        max-width: 120px !important;
        height: auto !important;
        object-fit: contain;
        margin-left: 15px;
        transition: transform 0.3s ease, filter 0.3s ease;
        cursor: pointer;
    }

    /* Hover Effects - Zoom/Scale Animation */
    .brand-logo:hover .logo-abbr,
    .brand-logo:hover .pti-logo-abbr,
    .brand-logo:hover img,
    a:hover .logo-abbr,
    a:hover .pti-logo-abbr,
    a:hover img[class*="logo"],
    .admin-logo:hover img,
    .unfold-logo:hover img,
    [class*="logo"]:hover img {
        transform: scale(1.15) rotate(5deg) !important;
        filter: brightness(1.2) contrast(1.1) !important;
    }

    .brand-logo:hover .brand-title,
    .brand-logo:hover .pti-logo-title {
        transform: scale(1.08) !important;
        filter: brightness(1.1) contrast(1.1) !important;
    }

    /* Admin Specific Logo Styling */
    .unfold-logo img,
    .admin-logo img,
    .django-admin img,
    [class*="admin"] img[class*="logo"] {
        transition: transform 0.3s ease, filter 0.3s ease !important;
        cursor: pointer !important;
    }

    /* Enhanced Hover for Admin */
    .unfold-logo:hover img,
    .admin-logo:hover img,
    .django-admin:hover img,
    [class*="admin"]:hover img[class*="logo"] {
        transform: scale(1.12) rotate(3deg) !important;
        filter: brightness(1.15) contrast(1.1) drop-shadow(0 2px 4px rgba(0,0,0,0.1)) !important;
    }

    /* Responsive adjustments */
    @media only screen and (max-width: 575px) {
        .logo-abbr, .pti-logo-abbr,
        .brand-logo img, .admin-logo img {
            max-width: 35px !important;
            min-width: 35px !important;
        }

        .brand-logo:hover .logo-abbr,
        .brand-logo:hover .pti-logo-abbr,
        .brand-logo:hover img {
            transform: scale(1.1) rotate(3deg) !important;
        }
    }

    @media only screen and (max-width: 1023px) {
        .brand-title, .pti-logo-title {
            display: none !important;
        }
    }

    /* Sidebar style adjustments */
    [data-sidebar-style="compact"] .brand-title,
    [data-sidebar-style="compact"] .pti-logo-title,
    [data-sidebar-style="mini"] .brand-title,
    [data-sidebar-style="mini"] .pti-logo-title {
        display: none !important;
    }

    [data-sidebar-style="mini"] .logo-abbr,
    [data-sidebar-style="mini"] .pti-logo-abbr {
        display: block !important;
    }

    /* Dark theme adjustments */
    [data-theme-version="dark"] .logo-abbr,
    [data-theme-version="dark"] .pti-logo-abbr,
    [data-theme-version="dark"] .brand-title,
    [data-theme-version="dark"] .pti-logo-title,
    [data-theme-version="dark"] img[class*="logo"] {
        filter: brightness(1.2) contrast(1.1) !important;
    }

    [data-theme-version="dark"] .brand-logo:hover .logo-abbr,
    [data-theme-version="dark"] .brand-logo:hover .pti-logo-abbr,
    [data-theme-version="dark"] .brand-logo:hover img {
        filter: brightness(1.4) contrast(1.2) drop-shadow(0 2px 6px rgba(255,255,255,0.1)) !important;
    }

    /* Active/Focus states */
    .brand-logo:active .logo-abbr,
    .brand-logo:active .pti-logo-abbr,
    .brand-logo:active img {
        transform: scale(1.05) !important;
    }

    .brand-logo:focus .logo-abbr,
    .brand-logo:focus .pti-logo-abbr,
    .brand-logo:focus img {
        outline: 2px solid #007bff !important;
        outline-offset: 2px !important;
        border-radius: 8px !important;
    }

    /* Animation keyframes for extra effects */
    @keyframes logoGlow {
        0% { filter: brightness(1) contrast(1); }
        50% { filter: brightness(1.3) contrast(1.2); }
        100% { filter: brightness(1) contrast(1); }
    }

    /* Special hover effect for admin */
    .admin-logo:hover img,
    .unfold-logo:hover img {
        animation: logoGlow 0.6s ease-in-out !important;
    }

    /* Ensure logo visibility in all themes */
    .logo-abbr, .pti-logo-abbr,
    .brand-title, .pti-logo-title,
    img[class*="logo"] {
        opacity: 1 !important;
        visibility: visible !important;
    }
</style>

</head>
<body>




{% comment %}     
<!-- TOASTR CONFIGURATION -->
<script defer>
    document.addEventListener("DOMContentLoaded", function () {
        toastr.options = {
            closeButton: true,
            progressBar: true,
            timeOut: 4000,
            extendedTimeOut: 1000,
            positionClass: "toast-top-right",
            preventDuplicates: true,
            showMethod: "slideDown",
            hideMethod: "slideUp",
            showDuration: 300,
            hideDuration: 300,
            newestOnTop: true,
            tapToDismiss: true
        };

        {% if messages %}
            {% for message in messages %}
                {% if 'success' in message.tags %}
                    toastr.success("{{ message|escapejs }}", "✅ Success");
                {% elif 'info' in message.tags %}
                    toastr.info("{{ message|escapejs }}", "ℹ️ Information");
                {% elif 'warning' in message.tags %}
                    toastr.warning("{{ message|escapejs }}", "⚠️ Warning");
                {% elif 'error' in message.tags %}
                    toastr.error("{{ message|escapejs }}", "❌ Error");
                {% else %}
                    toastr.info("{{ message|escapejs }}", "🔔 Notification");
                {% endif %}
            {% endfor %}
        {% endif %}
    });
</script> {% endcomment %}

<!-- CUSTOM TOAST STYLE -->
<style>
    /* Toast Container */
    .toast-top-right {
        top: 20px !important;
        right: 20px !important;
        position: fixed !important;
        z-index: 9999 !important;
    }

    /* Toast Base Styles */
    .toast {
        font-size: 14px !important;
        padding: 16px 20px !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
        min-width: 320px !important;
        max-width: 420px !important;
        line-height: 1.5 !important;
        border: 1px solid #e5e7eb !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        background-color: #ffffff !important;
    }

    /* Toast Title */
    .toast-title {
        font-weight: 600 !important;
        margin-bottom: 6px !important;
        font-size: 15px !important;
        display: flex !important;
        align-items: center !important;
    }

    /* Toast Message */
    .toast-message {
        margin: 0 !important;
        opacity: 0.85 !important;
        font-size: 13px !important;
    }

    /* Success Toast */
    .toast-success {
        background-color: #ffffff !important;
        color: #065f46 !important;
        border-left: 4px solid #10b981 !important;
    }

    .toast-success .toast-title {
        color: #047857 !important;
    }

    /* Error Toast */
    .toast-error {
        background-color: #ffffff !important;
        color: #991b1b !important;
        border-left: 4px solid #ef4444 !important;
    }

    .toast-error .toast-title {
        color: #dc2626 !important;
    }

    /* Info Toast */
    .toast-info {
        background-color: #ffffff !important;
        color: #1e40af !important;
        border-left: 4px solid #3b82f6 !important;
    }

    .toast-info .toast-title {
        color: #2563eb !important;
    }

    /* Warning Toast */
    .toast-warning {
        background-color: #ffffff !important;
        color: #92400e !important;
        border-left: 4px solid #f59e0b !important;
    }

    .toast-warning .toast-title {
        color: #d97706 !important;
    }

    /* Close Button */
    .toast-close-button {
        color: #6b7280 !important;
        font-size: 16px !important;
        font-weight: bold !important;
        opacity: 0.7 !important;
        background: none !important;
        border: none !important;
        padding: 0 !important;
        margin-left: 8px !important;
    }

    .toast-close-button:hover {
        opacity: 1 !important;
        color: #374151 !important;
    }

    /* Progress Bar */
    .toast-progress {
        height: 3px !important;
        border-radius: 0 0 12px 12px !important;
    }

    .toast-success .toast-progress {
        background-color: #10b981 !important;
    }

    .toast-error .toast-progress {
        background-color: #ef4444 !important;
    }

    .toast-info .toast-progress {
        background-color: #3b82f6 !important;
    }

    .toast-warning .toast-progress {
        background-color: #f59e0b !important;
    }

    /* Hover Effects */
    .toast:hover {
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15) !important;
        transform: translateY(-2px) !important;
        transition: all 0.3s ease !important;
    }

    /* Animation */
    .toast {
        transition: all 0.3s ease !important;
    }

    /* Icon spacing in title */
    .toast-title {
        gap: 8px !important;
    }
</style>






  
    <div id="main-wrapper">

        <!--**********************************
            Nav header start
        ***********************************-->
        <div class="nav-header">
            <a href="{% url 'dashboard' %}" class="brand-logo">
                <img src="{% static 'img/pti.png' %}" alt="PTI Logo" class="logo-abbr pti-logo-abbr">
                <h4 class="brand-title pti-logo-title">PTI Grading System</h4>
            </a>
            

            <div class="nav-control">
                <div class="hamburger">
                    <span class="line"></span><span class="line"></span><span class="line"></span>
                </div>
            </div>
        </div>
  



<!--**********************************
    Header start
***********************************-->
<div class="header">
    <div class="header-content">
        <nav class="navbar navbar-expand">
            <div class="collapse navbar-collapse justify-content-between">
                <div class="header-left">
                    <div class="dashboard_bar">
                        {% comment %} <div class="input-group search-area d-lg-inline-flex d-none me-5">
                          <span class="input-group-text" id="header-search">
                                <a href="javascript:void(0);">
                                    <i class="flaticon-381-search-2"></i>
                                </a>
                          </span>
                          <input type="text" class="form-control" placeholder="Search here" aria-label="Username" aria-describedby="header-search">
                        </div> {% endcomment %}

                    </div>
                </div>
                <ul class="navbar-nav header-right">
                    <li class="nav-item dropdown notification_dropdown">
                          <a class="nav-link bell dz-theme-mode" href="javascript:void(0);">
                            <i id="icon-light" class="fas fa-sun"></i>
                             <i id="icon-dark" class="fas fa-moon"></i>
                                    
                          </a>
                    </li>


           


                    <li class="nav-item dropdown header-profile">
                        <a class="nav-link" href="#" role="button" data-bs-toggle="dropdown">
                            {% if profile.image %}
                            <img src="{{profile.image.url}}" width="20" alt="" class="rounded-circle">
                            {% else %}
                                <img src="" width="20" alt="" class="rounded-circle">

                            {% endif %}
                            <div class="header-info">
                                <span>{{user.username}}</span>
                                <small>{{user.position}}</small>
                            </div>
                            <i class="fa fa-caret-down ms-3 me-2 " aria-hidden="true"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end">
                            <a href="{% url 'update_profile' %}" class="dropdown-item ai-icon">
                                <svg id="icon-user1" xmlns="http://www.w3.org/2000/svg" class="text-primary" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                                <span class="ms-2">Profile </span>
                            </a>
                  
                            <a href="{% url 'logout' %}" class="dropdown-item ai-icon">
                                <svg id="icon-logout" xmlns="http://www.w3.org/2000/svg" class="text-danger" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path><polyline points="16 17 21 12 16 7"></polyline><line x1="21" y1="12" x2="9" y2="12"></line></svg>
                                <span class="ms-2">Logout </span>
                            </a>
                        </div>
                    </li>
           
                </ul>
            </div>
        </nav>
    </div>
</div>




        <div class="deznav">
    <div class="deznav-scroll">
        <ul class="metismenu" id="menu">






<li><a href="{% url 'dashboard' %}" class="ai-icon" aria-expanded="false">
    <i class="flaticon-381-home"></i> <!-- Dashboard icon -->
    <span class="nav-text">Dashboard</span>
</a></li>







{% if user.position == 'lecturer' %}
<!-- 📚 LECTURER MENU ACTIVE -->
<li><a href="{% url 'grading' %}" class="ai-icon" aria-expanded="false">
        <i class="flaticon-013-checkmark text-warning"></i>
    <span class="nav-text">Grade Students</span>
</a></li>





<li><a href="{% url 'lecturer_results_view' %}" class="ai-icon" aria-expanded="false">
<i class="fa-solid fa-file-lines text-success"></i>  <!-- Result sheet -->
    <span class="nav-text">My Students Results</span>
</a></li>








<!-- Debug: Lecturer menu items above -->



{% elif user.position == 'hod' %}
<!-- 👨‍💼 HOD MENU ACTIVE -->

<li><a href="{% url 'submitted_grades' %}" class="ai-icon" aria-expanded="false">
        <i class="flaticon-013-checkmark text-warning"></i>
    <span class="nav-text">Submitted Grade</span>
</a></li>


<li><a href="{% url 'view_all_results' %}" class="ai-icon" aria-expanded="false">
<i class="fa-solid fa-file-lines text-success"></i>  <!-- Result sheet -->
    <span class="nav-text">View Students Results</span>
</a></li>




{% elif user.position == 'student' %}
<!-- 🎓 STUDENT MENU ACTIVE -->
<li><a href="{% url 'student_my_results' %}" class="ai-icon" aria-expanded="false">
<i class="fa-solid fa-file-lines text-success"></i>  <!-- Result sheet -->
    <span class="nav-text">My Results</span>
</a></li>

<li><a href="{% url 'student_performance' %}" class="ai-icon" aria-expanded="false">
    <i class="fas fa-chart-line text-primary"></i> <!-- Performance analytics icon -->
    <span class="nav-text">My Performance</span>
</a></li>




{% endif %}







<li><a href="{% url 'update_profile' %}" class="ai-icon" aria-expanded="false">
    <i class="flaticon-381-user text-info"></i> <!-- Lecturer results icon -->
    <span class="nav-text">Profile</span>
</a></li>

<li><a href="{% url 'logout' %}" class="ai-icon" aria-expanded="false">
    <i class="flaticon-381-exit text-danger"></i> <!-- Logout/Exit icon -->
    <span class="nav-text">Logout</span>
</a></li>


        </ul>






  
    </div>
</div>		



















{% block content %}

{% endblock %}




		
		<div class="footer">
    <div class="copyright">
    </div>
</div>


	</div>

                        <script src="{% static 'public/vendor/global/global.min.js' %}" type="text/javascript"></script>
                    <script src="{% static 'public/vendor/bootstrap-select/dist/js/bootstrap-select.min.js' %}" type="text/javascript"></script>
                                    <script src="{% static 'public/vendor/bootstrap-datetimepicker/js/moment.js' %}" type="text/javascript"></script>
                    <script src="{% static 'public/vendor/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js' %}" type="text/javascript"></script>
                    <script src="{% static 'public/vendor/peity/jquery.peity.min.js' %}" type="text/javascript"></script>
                    <script src="{% static 'public/vendor/apexchart/apexchart.js' %}" type="text/javascript"></script>
                    <script src="{% static 'public/js/dashboard/dashboard-1.js' %}" type="text/javascript"></script>
                                    <script src="{% static 'public/js/custom.min.js' %}" type="text/javascript"></script>
                    <script src="{% static 'public/js/deznav-init.js' %}" type="text/javascript"></script>

<!-- SweetAlert2 -->
<script src="{% static 'public/vendor/sweetalert2/dist/sweetalert2.min.js' %}"></script>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


<script>
document.addEventListener('DOMContentLoaded', function () {
    {% if messages %}
        {% for message in messages %}
            // Detect current theme
            const isDarkTheme = document.body.classList.contains('dark') ||
                               document.documentElement.getAttribute('data-theme') === 'dark' ||
                               document.body.getAttribute('data-theme-version') === 'dark';

            // Theme-based configuration
            const themeConfig = {
                background: isDarkTheme ? '#2a2d3a' : '#ffffff',
                color: isDarkTheme ? '#ffffff' : '#333333',
                customClass: {
                    popup: isDarkTheme ? 'dark-theme-popup' : 'light-theme-popup',
                    title: isDarkTheme ? 'dark-theme-title' : 'light-theme-title',
                    content: isDarkTheme ? 'dark-theme-content' : 'light-theme-content'
                }
            };

            {% if message.tags == 'success' %}
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: '{{ message|escapejs }}',
                    confirmButtonText: 'OK',
                    background: themeConfig.background,
                    color: themeConfig.color,
                    confirmButtonColor: '#28a745',
                    iconColor: '#28a745',
                    customClass: themeConfig.customClass
                });
            {% elif message.tags == 'error' %}
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: '{{ message|escapejs }}',
                    confirmButtonText: 'OK',
                    background: themeConfig.background,
                    color: themeConfig.color,
                    confirmButtonColor: '#dc3545',
                    iconColor: '#dc3545',
                    customClass: themeConfig.customClass
                });
            {% elif message.tags == 'info' %}
                Swal.fire({
                    icon: 'info',
                    title: 'Information',
                    text: '{{ message|escapejs }}',
                    confirmButtonText: 'OK',
                    background: themeConfig.background,
                    color: themeConfig.color,
                    confirmButtonColor: '#17a2b8',
                    iconColor: '#17a2b8',
                    customClass: themeConfig.customClass
                });
            {% elif message.tags == 'warning' %}
                Swal.fire({
                    icon: 'warning',
                    title: 'Warning!',
                    text: '{{ message|escapejs }}',
                    confirmButtonText: 'OK',
                    background: themeConfig.background,
                    color: themeConfig.color,
                    confirmButtonColor: '#ffc107',
                    iconColor: '#ffc107',
                    customClass: themeConfig.customClass
                });
            {% endif %}
        {% endfor %}
    {% endif %}
});
</script>

<style>
/* Dark theme SweetAlert customization */
.dark-theme-popup {
    border: 1px solid #495057 !important;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5) !important;
}

.dark-theme-title {
    color: #ffffff !important;
    font-weight: 600 !important;
}

.dark-theme-content {
    color: #e9ecef !important;
}

/* Light theme SweetAlert customization */
.light-theme-popup {
    border: 1px solid #dee2e6 !important;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15) !important;
    background: #ffffff !important;
}

.light-theme-title {
    color: #333333 !important;
    font-weight: 600 !important;
}

.light-theme-content {
    color: #666666 !important;
}

/* Button hover effects */
.swal2-confirm:hover {
    opacity: 0.9 !important;
    transform: translateY(-1px) !important;
}

/* Animation improvements */
.swal2-popup {
    animation-duration: 0.3s !important;
}
</style>

</body>

</html>














































