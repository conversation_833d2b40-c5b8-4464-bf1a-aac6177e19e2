{% extends "dashboard/base.html" %}
{% load static %}

{% block title %}My Results - {{ student.full_name }}{% endblock %}

{% block content %}

<div class="content-body">
    <div class="container-fluid">

        <div class="form-head d-flex flex-wrap mb-sm-4 mb-3 align-items-center">
            <div class="me-auto d-lg-block mb-3">
                <h2 class="text-black mb-0 font-w700">My Academic Results</h2>
                <p class="text-muted mb-0">
                    <i class="fas fa-user-graduate me-2"></i>
                    {{ student.full_name }} - {{ student.matric_number }} - {{ student.department.name }}
                </p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
      
                    <br>

                    <!-- Filters -->
                    <div class="px-3 pb-3">
                        <form method="GET" class="bg-light rounded p-3 mb-3">
                            <div class="row g-3 align-items-center">
                                <div class="col-md-2">
                                    <h6 class="mb-0 text-muted">
                                        <i class="fas fa-filter me-2"></i>Select Filters:
                                    </h6>
                                </div>

                                <!-- Level Filter -->
                                <div class="col-md-3">
                                    <label class="form-label mb-1 text-muted small">
                                        <i class="fas fa-layer-group me-1"></i>Level <span class="text-danger">*</span>
                                    </label>
                                    <select name="level" class="form-select form-select-sm" required>
                                        <option value="">Select Level...</option>
                                        {% for level in levels %}
                                        <option value="{{ level.id }}" {% if level.id|stringformat:"s" == level_id %}selected{% endif %}>
                                            {{ level.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <!-- Semester Filter -->
                                <div class="col-md-3">
                                    <label class="form-label mb-1 text-muted small">
                                        <i class="fas fa-calendar me-1"></i>Semester <span class="text-danger">*</span>
                                    </label>
                                    <select name="semester" class="form-select form-select-sm" required>
                                        <option value="">Select Semester...</option>
                                        {% for semester in semesters %}
                                        <option value="{{ semester.id }}" {% if semester.id|stringformat:"s" == semester_id %}selected{% endif %}>
                                            {{ semester.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <!-- Search Button -->
                                <div class="col-md-4" style="margin-top: 1em;">
                                    <button type="submit" class="btn btn-primary btn-sm me-2">
                                        <i class="fas fa-search me-1"></i>View My Result
                                    </button>
                                    <a href="{% url 'student_my_results' %}" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-times me-1"></i>Clear
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Results Display -->
                    {% if show_results %}
                        {% if student_result %}


                        <div class="card-body" id="printable-result-section">
                            <!-- Result Header -->
<div class="row mb-4" id="printable-result-section">
                                <div class="col-md-8">
                                    <h4 class="text-primary mb-2">
                                        <i class="fas fa-graduation-cap me-2"></i>
                                        {{ selected_level.name }} - {{ selected_semester.name }} Semester Results
                                    </h4>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p class="mb-1"><strong>Name:</strong> {{ student.full_name }}</p>
                                            <p class="mb-1"><strong>Matric Number:</strong> {{ student.matric_number }}</p>
                                            <p class="mb-1"><strong>Department:</strong> {{ student.department.name }}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p class="mb-1"><strong>Level:</strong> {{ selected_level.name }}</p>
                                            <p class="mb-1"><strong>Semester:</strong> {{ selected_semester.name }}</p>
                                            <!-- <p class="mb-1"><strong>Status:</strong> 
                                                {% if student_result.status == 'published' %}
                                                    <span class="text-success fw-bold">Published</span>
                                                {% else %}
                                                    <span class="text-warning fw-bold">Draft</span>
                                                {% endif %}
                                            </p> -->
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h3 class="mb-1" style="color: white;">{{ student_result.gpa|floatformat:2 }}</h3>
                                            <p class="mb-0">GPA</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Course Results Table -->
                            <div class="table-responsive mb-4">
                                <table class="table table-bordered">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>S/N</th>
                                            <th>Course Code</th>
                                            <th>Course Title</th>
                                            <th>Credit Unit</th>
                                            <th>Score (%)</th>
                                            <th>Grade</th>
                                            <th>Grade Point</th>
                                            <th>TCP</th>
                                            <th>CGPA</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for grade in student_result.grades_list %}
                                        <tr>
                                            <td class="text-center">{{ forloop.counter }}</td>
                                            <td class="fw-bold">{{ grade.course.code }}</td>
                                            <td>{{ grade.course.title }}</td>
                                            <td class="text-center">{{ grade.course.credit_unit }}</td>
                                            <td class="text-center">{{ grade.score }}%</td>
                                            <td class="text-center fw-bold">{{ grade.grade }}</td>
                                            <td class="text-center">{{ grade.grade_point|floatformat:2 }}</td>
                                            <td class="text-center text-primary fw-bold">{{ grade.grade_point|floatformat:2 }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot class="table-light">
                                        <tr>
                                            <th colspan="3">TOTALS</th>
                                            <th class="text-center">{{ student_result.tcu }}</th>
                                            <th colspan="3"></th>
                                            <th class="text-center text-primary fw-bold">{{ student_result.tgp|floatformat:2 }}</th>
                                            <th class="text-center text-dark fw-bold">{{ student_result.gpa|floatformat:2 }}</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>

                            <!-- Summary Cards -->
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h4 class="mb-1">{{ student_result.total_courses }}</h4>
                                            <p class="mb-0">Courses</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-secondary text-white">
                                        <div class="card-body text-center">
                                            <h4 class="mb-1">{{ student_result.tcu }}</h4>
                                            <p class="mb-0">Total Credit Units</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h4 class="mb-1">{{ student_result.tgp|floatformat:2 }}</h4>
                                            <p class="mb-0">Total Grade Points</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card {% if student_result.remark == 'passed' %}bg-success{% else %}bg-danger{% endif %} text-white">
                                        <div class="card-body text-center">
                                            <h4 class="mb-1">{{ student_result.gpa|floatformat:2 }}</h4>
                                            <p class="mb-0">
                                                {% if student_result.remark == 'passed' %}
                                                    PASSED
                                                {% else %}
                                                    FAILED
                                                {% endif %}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Course Performance Pie Chart -->
                            <div class="row mt-4">
                                <div class="col-lg-6">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-header bg-warning text-white">
                                            <h5 class="mb-0">
                                                <i class="fas fa-chart-pie me-2"></i>Course Performance Pie Chart
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="coursePerformancePieChart" height="200"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-header bg-success text-white">
                                            <h5 class="mb-0">
                                                <i class="fas fa-trophy me-2"></i>Grade Distribution
                                            </h5>
                                        </div>
                                        <div class="card-body">
                                            <div id="gradeStats">
                                                <!-- Grade statistics will be populated by JavaScript -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                 

                            <!-- Print Button -->
                            <div class="text-center mt-4">
                                <button class="btn btn-outline-primary" onclick="printResultSection()">
                                    <i class="fas fa-print me-2"></i>
                                    Print This Section
                                </button>
                            </div>
                        </div>


                        {% else %}
                        <!-- No Result Found -->
                        <div class="card-body text-center py-5">
                            <div class="mb-3">
                                <i class="fas fa-search fa-4x text-muted"></i>
                            </div>
                            <h5 class="text-muted">No Result Found</h5>
                            <p class="text-muted">
                                No result found for <strong>{{ selected_level.name }}</strong> - <strong>{{ selected_semester.name }}</strong>.
                                <br>Your result may not have been generated yet or you may not have courses in this level/semester.
                            </p>
                            <div class="alert alert-info border-0 d-inline-block">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Note:</strong> Results are generated automatically when your grades are approved.
                            </div>
                        </div>
                        {% endif %}
                    {% else %}
                        <!-- Default State - No Filters Selected -->
                        <div class="card-body text-center py-5">
                            <div class="mb-4">
                                <i class="fas fa-filter fa-4x text-muted"></i>
                            </div>
                            <h4 class="text-muted mb-3">Select Level and Semester</h4>
                            <p class="text-muted mb-4">
                                Please select both <strong>Level</strong> and <strong>Semester</strong> from the filters above to view your result.
                                <br>Your academic performance will be displayed in a detailed format.
                            </p>
                            <div class="alert alert-info border-0 d-inline-block">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Note:</strong> You can only view results that have been published by your department.
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Chart.js Pie Chart Script -->
{% if student_result and student_result.grades_list %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var ctx = document.getElementById('coursePerformancePie').getContext('2d');
        var courseLabels = [
            {% for grade in student_result.grades_list %}'{{ grade.course.code }}'{% if not forloop.last %}, {% endif %}{% endfor %}
        ];
        var courseScores = [
            {% for grade in student_result.grades_list %}{{ grade.score }}{% if not forloop.last %}, {% endif %}{% endfor %}
        ];
        var backgroundColors = [
            '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796', '#5a5c69', '#fd7e14', '#20c997', '#6f42c1'
        ];
        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: courseLabels,
                datasets: [{
                    data: courseScores,
                    backgroundColor: backgroundColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    title: {
                        display: true,
                        text: 'Performance by Course (Score %)',
                        font: { size: 16 }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.label || '';
                                var value = context.parsed || 0;
                                return label + ': ' + value + '%';
                            }
                        }
                    }
                }
            }
        });
    });
</script>
{% endif %}

<script>
function printResultSection() {
    var printContents = document.getElementById('printable-result-section').outerHTML;
    var printWindow = window.open('', '', 'height=600,width=900');
    printWindow.document.write('<html><head><title>Print Result Section</title>');
    printWindow.document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">');
    printWindow.document.write('<style>body{font-size:12px;} .table-bordered{border:2px solid #000!important;} .table-bordered th,.table-bordered td{border:1px solid #000!important;} .card{border:none!important;box-shadow:none!important;} </style>');
    printWindow.document.write('</head><body>');
    printWindow.document.write(printContents);
    printWindow.document.write('</body></html>');
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
}
</script>

{% if show_results and student_result %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Course performance data from Django template
    const courseData = [
        {% for grade in student_result.grades_list %}
        {
            courseCode: '{{ grade.course.code }}',
            courseTitle: '{{ grade.course.title }}',
            score: {{ grade.score }},
            grade: '{{ grade.grade }}',
            gradePoint: {{ grade.grade_point|floatformat:2 }},
            creditUnit: {{ grade.course.credit_unit }}
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];

    createCoursePerformancePieChart(courseData);
    createGradeStatistics(courseData);
});

function createCoursePerformancePieChart(courseData) {
    const ctx = document.getElementById('coursePerformancePieChart').getContext('2d');
    // Prepare data for pie chart
    const labels = courseData.map(course => course.courseCode);
    const scores = courseData.map(course => course.score);
    // Generate colors for each course
    const backgroundColors = labels.map((_, i) => `hsl(${i * 360 / labels.length}, 70%, 60%)`);
    new Chart(ctx, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                data: scores,
                backgroundColor: backgroundColors,
                borderColor: '#fff',
                borderWidth: 2,
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        generateLabels: function(chart) {
                            const data = chart.data;
                            return data.labels.map((label, index) => {
                                const course = courseData[index];
                                return {
                                    text: `${label} (${course.score}%)`,
                                    fillStyle: data.datasets[0].backgroundColor[index],
                                    strokeStyle: data.datasets[0].borderColor,
                                    lineWidth: 2,
                                    hidden: false,
                                    index: index
                                };
                            });
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            const index = context[0].dataIndex;
                            const course = courseData[index];
                            return `${course.courseCode} - ${course.courseTitle}`;
                        },
                        label: function(context) {
                            const index = context.dataIndex;
                            const course = courseData[index];
                            return [
                                `Score: ${course.score}%`,
                                `Grade: ${course.grade}`,
                                `Credit Unit: ${course.creditUnit}`
                            ];
                        }
                    }
                }
            }
        }
    });
}

function createGradeStatistics(courseData) {
    const gradeStatsContainer = document.getElementById('gradeStats');
    // Calculate grade distribution
    const gradeOrder = ['A', 'AB','B', 'BC', 'C', 'D', 'E', 'F'];
    const gradeColors = {
        'A': 'bg-success',
        'AB': 'bg-info',
        'B': 'bg-primary',
        'BC': 'bg-dark',
        'C': 'bg-info',
        'D': 'bg-warning',
        'E': 'bg-secondary',
        'F': 'bg-danger'
    };
    const gradeDistribution = {};
    gradeOrder.forEach(g => gradeDistribution[g] = 0);
    courseData.forEach(course => {
        const grade = course.grade;
        if (gradeDistribution.hasOwnProperty(grade)) {
            gradeDistribution[grade]++;
        }
    });
    // Generate HTML for grade statistics
    let statsHTML = '<div class="row">';
    let totalCourses = courseData.length;
    gradeOrder.forEach(grade => {
        const count = gradeDistribution[grade];
        if (count > 0) {
            const percentage = ((count / totalCourses) * 100).toFixed(2);
            statsHTML += `
                <div class="col-6 col-md-4 mb-3">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <h5 class="mb-0">${grade}</h5>
                            <p class="mb-0">${count} course${count > 1 ? 's' : ''} <span class='text-muted'>(${percentage}%)</span></p>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar ${gradeColors[grade]}" role="progressbar" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    });
    statsHTML += '</div>';
    gradeStatsContainer.innerHTML = statsHTML;
}
</script>
{% endif %}
{% endblock %}
